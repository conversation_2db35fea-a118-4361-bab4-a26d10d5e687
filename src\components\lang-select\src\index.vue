<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-03-10 11:32:51
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-22 14:39:25
 * @FilePath: \ems_manage_copy\src\components\lang-select\src\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref } from 'vue'
import { useLocale } from 'vuetify'
import { initLocale, langArr } from '@/locale'
import { useRouter } from 'vue-router'

const router = useRouter()
const { current } = useLocale()
const langTitle = ref()
const init = () => {
  current.value = current.value ?? 'zhHans'
  langTitle.value = current.value
    ? langArr.find((item) => item.value == current.value)?.title
    : '简体中文'
}
init()
const handleChangeLang = (lang) => {
  console.log('lang', lang)
  langTitle.value = langArr.find((item) => item.value == lang).title
  current.value = lang
  localStorage.setItem('lang', lang)
  initLocale(lang)
  router.go(0)
}
</script>

<template>
  <v-menu>
    <template v-slot:activator="{ props }">
      <v-btn
        append-icon="mdi-translate"
        size="large"
        variant="plain"
        v-bind="props"
        >{{ langTitle }}</v-btn
      >
    </template>

    <v-list>
      <v-list-item
        v-for="item in langArr"
        :key="item.value"
        class="cursor-pointer"
      >
        <v-list-item-title @click="handleChangeLang(item.value)">{{
          item.title
        }}</v-list-item-title>
      </v-list-item>
    </v-list>
  </v-menu>
</template>

<style lang="scss" scoped></style>
