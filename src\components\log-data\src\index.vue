<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-07-24 11:54:21
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-28 17:49:03
 * @FilePath: \ems_manage\src\components\log-data\src\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref } from 'vue'
import { useConfigStore } from '@/store/module/config'

const model = defineModel({ default: () => [] })

const handleScroll = () => {
  const messagesContainer = document.querySelector('.message')
  if (!messagesContainer) return
  const threshold = 10 // 容差，允许一点误差
  const isBottom =
    messagesContainer.scrollHeight - messagesContainer.scrollTop <=
    messagesContainer.clientHeight + threshold
  useConfigStore().isAtBottom = isBottom
}
</script>

<template>
  <div class="log-cont pa-4 rounded-lg flex flex-column overflow-hidden">
    <div class="overflow-y-auto message" @scroll="handleScroll">
      <div class="flex flex-column justify-end">
        <div v-for="item in model" :key="item" v-html="item"></div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.log-cont {
  background-color: #000;
  color: #fff;
  height: 550px;
}
.message {
  flex: 1;
}
</style>
