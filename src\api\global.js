/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-08-22 17:13:06
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-08-25 17:32:49
 * @FilePath: \ems_manage\src\api\global.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from './index'

// 获取语言列表
export const getLanguageList = () => {
  return request({
    url: '/languages/list',
    method: 'get'
  })
}

// 获取全部的语言文件
export const getLanguageAll = () => {
  return request({
    url: '/languages/all',
    method: 'get'
  })
}

// 获取指定的语言文件
export const getLanguage = (queryInfo) => {
  return request({
    url: `/language/${queryInfo.fileName}`,
    method: 'get'
  })
}

// 导入语言文件（不覆盖）
export const setLanguageNo = (data) => {
  return request({
    url: '/language',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;'
    }
  })
}

// 导入语言文件（覆盖）
export const setLanguage = (data) => {
  return request({
    url: '/language',
    method: 'put',
    data,
    headers: {
      'Content-Type': 'application/json;'
    }
  })
}
