<script setup>
import {
  toRefs,
  computed,
  ref,
  reactive,
  getCurrentInstance,
  onUnmounted,
  nextTick,
  onMounted
} from 'vue'
import { useGlobalStore } from '@/store/global'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/store/module/user'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useDisplay, useLocale } from 'vuetify'
import { useConfigStore } from '@/store/module/config'
import dayjs, { formatDate } from '@/utils/date'

import LangSelect from '@/components/lang-select'

const { systemInfo, localTime, localTimeZone } = toRefs(useConfigStore())
const { current } = useLocale()
const { mobile } = useDisplay()
const { proxy } = getCurrentInstance()
const router = useRouter()
const { t } = useI18n()
const { meta } = useRoute()
const { userInfo, roles } = toRefs(useUserStore())
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard,
  showPanel,
  panelType
} = toRefs(useGlobalStore())
/**
 * 修改密码
 */
const userDialog = ref(false)
const typeText = ref('edit')
const handleUser = (type) => {
  typeText.value = type
  form.value.user_name = type == 'edit' ? userInfo.value.user_name : ''
  userDialog.value = true
}
const form = ref({
  user_name: userInfo.value.user_name,
  new_password: '',
  old_password: userInfo.value.password,
  confirmPassword: ''
})
const newPasswordRules = ref([(v) => !!v || t('密码必填')])
const confirmPasswordRules = ref([
  (v) => !!v || t('密码必填'),
  (v) => v == form.value.new_password || t('两次输入的密码不一致')
])

const loading = ref(false)
const submit = async () => {
  const { valid } = await proxy.$refs.editForm.validate()
  if (!valid) return
  loading.value = true
  let api = typeText.value == 'edit' ? 'editPwdFn' : 'resetPwdFn'
  let data =
    typeText.value == 'edit'
      ? {
          user_name: userInfo.value.user_name,
          new_password: form.value.new_password,
          old_password: userInfo.value.password
        }
      : {
          user_name: form.value.user_name,
          new_password: form.value.new_password
        }
  try {
    const res = await useUserStore()[api](data)
    snackbar.value = true
    snackbarText.value =
      typeText.value == 'edit' ? t('修改成功') : t('重置成功')
    loading.value = false
    userDialog.value = false
    if (typeText.value == 'edit') router.push('/login')
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
    loading.value = false
    userDialog.value = false
  }
}
const handleCancelClick = () => {
  loading.value = false
  userDialog.value = false
}

/**
 * 眼睛
 */
const eyeObj = reactive({
  old: false,
  new: false,
  confirm: false
})
const handleEyeClick = (type) => {
  if (type == 'old') {
    eyeObj.old = !eyeObj.old
  } else if (type == 'new') {
    eyeObj.new = !eyeObj.new
  } else if (type == 'confirm') {
    eyeObj.confirm = !eyeObj.confirm
  }
}

/**
 * 键盘
 */
keyboardMode.value = 'cn'
const handleShow = (e, value, prop) => {
  if (isShowKeyboard.value) return
  currentInput.value = prop
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  form.value[currentInput.value] = keyboardInputValue.value
  showKeyboard.value = false
  keyboardDialog.value = false
}
const keyboardArr = ref([
  {
    value: false,
    title: t('显示键盘')
  },
  {
    value: true,
    title: t('隐藏键盘')
  }
])
const handleKeyboardLang = (value) => {
  isShowKeyboard.value = value
  if (!isShowKeyboard.value) {
    snackbarText.value = t('键盘已开启')
  } else {
    snackbarText.value = t('键盘已关闭')
  }
  snackbar.value = true
}

/**
 * 时间
 */
const time = ref(null)
const getTime = () => {
  useConfigStore()
    .getLocalTimeFn()
    .then(() => {
      // 先清理旧的定时器
      if (time.value) {
        clearInterval(time.value)
      }

      time.value = setInterval(() => {
        localTime.value = dayjs(localTime.value)
          .add(1, 's')
          .format('YYYY-MM-DD HH:mm:ss')
      }, 1000)
    })
}
const handleTimeClick = () => {
  router.push('/system')
}
onMounted(() => {
  getTime()
})
onUnmounted(() => {
  if (time.value) {
    clearInterval(time.value)
    time.value = null
  }
})

/**
 * 日志
 */
const handleLog = () => {
  panelType.value = 'log'
  showPanel.value = true
}
</script>

<template>
  <v-app-bar>
    <v-app-bar-title
      class="cursor-pointer time-display"
      @click="handleTimeClick"
    >
      <div class="time-container">
        <span class="time-text">{{ localTime }}</span>
        <span class="timezone-text" v-if="localTimeZone"
          >({{ localTimeZone }})</span
        >
      </div>
    </v-app-bar-title>

    <!-- <v-btn
      append-icon="mdi-application-outline"
      size="large"
      variant="plain"
      @click="$router.push('/backLog')"
      v-if="userInfo.permission_level == 4"
    >
      {{ $t('后台日志') }}
    </v-btn> -->
    <v-btn
      append-icon="mdi-application-outline"
      size="large"
      variant="plain"
      @click="handleLog"
      v-if="userInfo.permission_level == 4"
    >
      {{ $t('后台日志') }}
    </v-btn>

    <lang-select />
    <v-menu>
      <template v-slot:activator="{ props }">
        <v-btn
          append-icon="mdi-keyboard"
          size="large"
          variant="plain"
          v-bind="props"
          >{{ $t('键盘') }}</v-btn
        >
      </template>

      <v-list>
        <v-list-item
          v-for="item in keyboardArr"
          :key="item.title"
          class="cursor-pointer"
        >
          <v-list-item-title @click="handleKeyboardLang(item.value)">{{
            item.title
          }}</v-list-item-title>
        </v-list-item>
      </v-list>
    </v-menu>

    <v-btn icon>
      <v-icon>mdi-dots-vertical</v-icon>
      <v-menu
        activator="parent"
        width="230"
        location="bottom end"
        offset="14px"
      >
        <v-list>
          <v-list-item
            class="cursor-pointer"
            @click="handleUser('reset')"
            v-if="userInfo.permission_level == 4"
          >
            <template #prepend>
              <v-icon icon="mdi-lock-reset" size="22" />
            </template>

            <v-list-item-title>{{ $t('重置密码') }}</v-list-item-title>
          </v-list-item>
          <v-list-item class="cursor-pointer" @click="handleUser('edit')">
            <template #prepend>
              <v-icon icon="mdi-lock" size="22" />
            </template>

            <v-list-item-title>{{ $t('修改密码') }}</v-list-item-title>
          </v-list-item>

          <v-divider class="my-2" />

          <!-- 👉 Logout -->
          <v-list-item to="/login">
            <template #prepend>
              <v-icon icon="mdi-logout" size="22" />
            </template>

            <v-list-item-title>Logout</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </v-btn>
  </v-app-bar>

  <v-dialog v-model="userDialog" width="auto">
    <v-card width="480" class="pa-4 rounded-lg">
      <v-card-title class="text-center mb-6">{{
        typeText == 'edit' ? $t('修改密码') : $t('重置密码')
      }}</v-card-title>
      <v-sheet class="mx-auto w-full">
        <v-form fast-fail @submit.prevent="submit" ref="editForm">
          <v-text-field
            v-model="form.user_name"
            :label="$t('账号')"
            :readonly="typeText == 'edit'"
            :rules="typeText == 'edit' ? [] : [(v) => !!v || $t('账号必填')]"
            @click:control="handleShow($event, form.user_name, 'user_name')"
          ></v-text-field>

          <v-text-field
            v-model="userInfo.password"
            :label="$t('旧密码')"
            :type="eyeObj.old ? 'text' : 'password'"
            :append-inner-icon="eyeObj.old ? 'mdi-eye' : 'mdi-eye-closed'"
            readonly
            @click:appendInner="handleEyeClick('old')"
            v-if="typeText == 'edit'"
          ></v-text-field>
          <v-text-field
            v-model="form.new_password"
            :rules="newPasswordRules"
            :label="$t('新密码')"
            :clearable="true"
            :type="eyeObj.new ? 'text' : 'password'"
            :append-inner-icon="eyeObj.new ? 'mdi-eye' : 'mdi-eye-closed'"
            @click:appendInner="handleEyeClick('new')"
            @click:control="
              handleShow($event, form.new_password, 'new_password')
            "
          ></v-text-field>
          <v-text-field
            v-model="form.confirmPassword"
            :rules="confirmPasswordRules"
            :label="$t('确认新密码')"
            :clearable="true"
            :type="eyeObj.confirm ? 'text' : 'password'"
            :append-inner-icon="eyeObj.confirm ? 'mdi-eye' : 'mdi-eye-closed'"
            @click:appendInner="handleEyeClick('confirm')"
            v-if="typeText == 'edit'"
            @click:control="
              handleShow($event, form.confirmPassword, 'confirmPassword')
            "
          ></v-text-field>
          <div class="d-flex justify-center">
            <v-btn
              class="mt-2 mr-4 px-8"
              height="50"
              @click="handleCancelClick"
              >{{ $t('取消') }}</v-btn
            >
            <v-btn
              class="mt-2 px-8"
              type="submit"
              height="50"
              :loading="loading"
              color="primary"
              >{{ $t('确定') }}</v-btn
            >
          </div>
        </v-form>
      </v-sheet>
    </v-card>
  </v-dialog>
</template>

<style lang="scss" scoped>
.time-display {
  .time-container {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .time-text {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      color: #333;
      transform: scale(1.02);
    }
  }

  .timezone-text {
    font-size: 14px;
    color: #666;
    font-weight: 400;
    opacity: 0.8;
  }
}

.time-display:hover {
  .time-text {
    animation: pulse 1s ease-in-out;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
</style>
