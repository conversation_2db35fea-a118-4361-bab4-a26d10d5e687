<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-08 18:15:54
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-31 16:48:28
 * @FilePath: \ems_manage\src\App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, toRefs, onUnmounted, onMounted } from 'vue'
import { registerMaotuCustomSvg } from '@/plugins/registerMaotuCustomSvg'
import { useGlobalStore } from '@/store/global'
import { useConfigStore } from '@/store/module/config'
import { initLocale } from '@/locale'
import { useRoute } from 'vue-router'

import DragPanel from '@/components/drag-panel'
import Log from '@/components/log.vue'

const route = useRoute()
const {
  snackbar,
  snackbarText,
  lang,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardInput,
  keyboardMode,
  showPanel,
  panelType
} = toRefs(useGlobalStore())
const { logData, logWs } = toRefs(useConfigStore())

initLocale()
registerMaotuCustomSvg()

/**
 * 键盘
 */
const manyDict = ref('dict/chowder.json')
const singleDict = ref('dict/baseDict.json')
keyboardDialog.value = false
const onFocus = (e) => {
  showKeyboard.value = e
}
const handleFocus = (e) => {
  e.mode = keyboardMode.value
  keyboardInput.value = e
}
const handleCancel = () => {
  showKeyboard.value = false
  keyboardDialog.value = false
}

/**
 * 面板
 */
const updateVisible = (data) => {
  showPanel.value = data
  if (panelType.value == 'log') {
    logData.value = []
    logWs.value?.close()
  }
}
onMounted(() => {
  window.addEventListener('beforeunload', () => {
    showPanel.value = false
    logData.value = []
    logWs.value?.close()
  })
})
onUnmounted(() => {
  showPanel.value = false
  logData.value = []
  logWs.value?.close()
})

/**
 * 语言
 */
useGlobalStore().getLanguageAllFn()
</script>

<template>
  <v-app class="bg-background overflow-auto">
    <router-view />
  </v-app>
  <v-snackbar v-model="snackbar">
    {{ snackbarText }}
  </v-snackbar>
  <v-dialog
    v-model="keyboardDialog"
    width="auto"
    :persistent="true"
    :no-click-animation="true"
  >
    <v-text-field
      v-model="keyboardInputValue"
      variant="solo-inverted"
      label=""
      hide-details
      @update:focused="onFocus"
      keyboard="true"
      @focus="handleFocus"
      autofocus
    ></v-text-field>
    <keyboard
      :transitionTime="'0.5s'"
      :maxQuantify="10"
      :showKeyboard="showKeyboard"
      float
      :manyDict="manyDict"
      :singleDict="singleDict"
      :blurHide="false"
      :inputEvent="keyboardInput"
      @confirm="confirmCall"
      @cancel="handleCancel"
    ></keyboard>
  </v-dialog>
  <DragPanel v-model:visible="showPanel" @update:visible="updateVisible">
    <template v-if="panelType == 'log'">
      <Log style="height: 100%" />
    </template>
  </DragPanel>
</template>

<style lang="scss" scoped>
:deep(.el-input-number) {
  .el-input-number__decrease,
  .el-input-number__increase {
    display: none !important;
  }
  .is-disabled {
    display: none !important;
  }
  .el-input__wrapper {
    padding: 0 0 0 15px !important;
    background: rgb(66, 66, 66) !important;
    box-shadow: 0 0 0 1px rgb(66, 66, 66) !important;
  }
  .el-input__wrapper.is-focus {
    box-shadow: 0 0 0 1px rgb(66, 66, 66);
  }
  .el-input__wrapper:hover {
    box-shadow: 0 0 0 1px rgb(66, 66, 66);
  }
  .el-input__inner {
    height: 56px !important;
    font-size: 16px !important;
    text-align: left !important;
    color: #fff !important;
  }
}
</style>
