import { defineStore, storeToRefs } from 'pinia'
import { ref } from 'vue'
import {
  getDeviceData,
  getFaultData,
  getDeviceList,
  getFaultPageInfo,
  batteryCluster,
  batteryData,
  getCurrentAlarm,
  exportAlarmRecordFile,
  exportAlarmData
} from '@/api/device'
import { i18n } from '@/locale'
import { isEmpty, maxBy, minBy } from 'lodash-es'
import { useConfigStore } from '@/store/module/config'
import { handleExport, sortFn } from '@/utils'
import { useGlobalStore } from '../global'

export const useDeviceStore = defineStore(
  'device',
  () => {
    const { snackbar, snackbarText } = storeToRefs(useGlobalStore())
    const treeData = ref([])
    const batteryTypeDeviceId = ref([])
    const getDeviceListFn = async () => {
      batteryTypeDeviceId.value = []
      const res = await getDeviceList()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      res.data.devices.forEach((item) => {
        if (item.device_class == 'BMSDevice' || item.device_class == 'BAUDevice') {
          batteryTypeDeviceId.value.push(item.device_id)
        }
        item.id = item.device_id
        item.module = false
        item.title = item.device_name
      })
      treeData.value = res.data.devices
    }

    const deviceData = ref({})
    const { deviceConfigData } = storeToRefs(useConfigStore())
    const getDeviceDataFn = async (queryInfo) => {
      const res = await getDeviceData(queryInfo)
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      let item = res.data
      // res.data.forEach((item) => {
      if (item.modules.length) {
        // 有模块
        deviceData.value[item.device_id] = {}
        item.modules.forEach((module, moduleIndex) => {
          deviceData.value[item.device_id][module.module_name] = {}
          // 配置
          let configIndex = deviceConfigData.value.findIndex(config => config.deviceId == item.device_id && config.moduleType == module.module_type)
          if (!isEmpty(module.module_type)) {
            let packNumber = configIndex != -1 ? deviceConfigData.value[configIndex].config.packNumber : 1
            let cellData = mapCellData(module.points[0].analog, packNumber)
            deviceData.value[item.device_id][module.module_name]['analog'] = module.points[0].analog
            deviceData.value[item.device_id][module.module_name]['cellData'] = cellData
          } else { // 无配置与无模块类型
            module.points.forEach((point) => {
              let key = Object.keys(point)[0]
              point[key].sort(sortFn)
              deviceData.value[item.device_id][module.module_name][key] =
                point[key]
            })
          }
        })
      } else {
        if (!item.points || !item.points.length) {
          deviceData.value = {}
          return
        }
        deviceData.value[item.device_id] = {}
        item.points.forEach((point) => {
          let key = Object.keys(point)[0]
          point[key].sort(sortFn)
          deviceData.value[item.device_id][key] = point[key]
        })
      }
      // })
    }
    const mapCellData = (data, packNumber = 1) => {
      // 初始化簇数据
      let clusterPointData = {}
      // 计算电芯温度数量
      let templateNumber = data.filter(item => item.point_id.indexOf('cellTemp') != -1).length / packNumber
      // 计算电芯电压数量
      let voltageNumber = data.filter(item => item.point_id.indexOf('cellVol') != -1).length / packNumber
      // 计算电芯数量
      let cellNumber = Math.max(templateNumber, voltageNumber)
      // 电池包
      for (let packIndex = 1; packIndex <= packNumber; packIndex++) {
        // 初始化包数据
        clusterPointData[`${packIndex}#_${i18n.global.t('包')}`] = {
          data: [],
          maxV: null,
          maxT: null,
          minV: null,
          minT: null,
          vUnit: null,
          tUnit: null,
        }
        // 电芯
        for (let j = 1; j <= cellNumber; j++) {
          // 电芯电压
          let cellVolValue = data.find(item => item.point_name.split('#')[0] == ((voltageNumber * packIndex) - (voltageNumber - j)) && item.point_id.indexOf('cellVol') != -1)
          // 电芯温度
          let cellTempValue = data.find(item => item.point_name.split('#')[0] == ((templateNumber * packIndex) - (templateNumber - j)) && item.point_id.indexOf('cellTemp') != -1)
          let point = {
            voltage: cellVolValue?.value ?? '',
            vUnit: cellVolValue?.unit ?? '',
            temperature: cellTempValue?.value ?? '',
            tUnit: cellTempValue?.unit ?? '',
          }
          clusterPointData[`${packIndex}#_${i18n.global.t('包')}`].data.push(point)
        }
        // 计算包最值
        let pointData = clusterPointData[`${packIndex}#_${i18n.global.t('包')}`].data.map(item => {
          item.voltage = Number(item.voltage)
          item.temperature = Number(item.temperature)
          return item
        })
        let maxValue = maxBy(pointData, (item) => {
          if (item.voltage != null || item.voltage == 0) return item.voltage
        })
        let minValue = minBy(pointData, (item) => {
          if (item.temperature !== null || item.temperature == 0) return item.temperature
        })
        clusterPointData[`${packIndex}#_${i18n.global.t('包')}`].maxV = maxValue?.voltage
        clusterPointData[`${packIndex}#_${i18n.global.t('包')}`].vUnit = maxValue?.vUnit
        clusterPointData[`${packIndex}#_${i18n.global.t('包')}`].maxT = maxBy(pointData, (item) => {
          if (item.temperature !== null || item.temperature == 0) return item.temperature
        })?.temperature
        clusterPointData[`${packIndex}#_${i18n.global.t('包')}`].minV = minBy(pointData, (item) => {
          if (item.voltage != null || item.voltage == 0) return item.voltage
        })?.voltage
        clusterPointData[`${packIndex}#_${i18n.global.t('包')}`].minT = minValue?.temperature
        clusterPointData[`${packIndex}#_${i18n.global.t('包')}`].tUnit = minValue?.tUnit
      }
      // 返回簇数据
      return clusterPointData
    }

    const greenList = ref(['闭合', '运行', '正常', '是', '开启', '有', '打开', '可用', '高压已连接'])
    const redList = ref(['断开', '停止', '故障', '否', '无效', '告警', '关闭', '无', '不可用', '高压已断开', '紧急停止'])
    const getStatusTypeFn = (status) => {
      if (greenList.value.findIndex(item => item == status) !== -1) return 1
      else if (redList.value.findIndex(item => item == status) !== -1) return 2
      else return 3
    }

    /**
     * 告警数据
     */
    const pageInfo = ref({
      total: 0,
      pageSize: 10,
      pageIndex: 1
    })
    const faultQueryInfo = ref({})
    const faultData = ref([])
    const getFaultDataFn = async (queryInfo) => {
      // faultData.value = []
      const res = await getFaultData({
        pageSize: pageInfo.value.pageSize,
        pageIndex: pageInfo.value.pageIndex,
        ...faultQueryInfo.value,
        ...queryInfo
      })
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      pageInfo.value.total = res.data.count
      faultData.value = res.data.faults?.length ? res.data.faults : []
    }
    const currentAlarmCount = ref(0)
    const getCurrentAlarmFn = async () => {
      const res = await getCurrentAlarm()
      if (res.code !== 200) {
        currentAlarmCount.value = 0
        snackbar.value = true
        snackbarText.value = res.msg
      }
      currentAlarmCount.value = res.data.count
    }

    /**
     * 电池数据
     */
    const clusterData = ref([])
    const clusterPointData = ref({})
    const batteryClusterFn = async (queryInfo) => {
      const res = await batteryCluster(queryInfo)
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      clusterData.value = res.data
    }
    const batteryDataFn = async (queryInfo) => {
      const res = await batteryData(queryInfo)
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      if (res?.data.points) {
        let data = res.data.points
        clusterPointData.value[queryInfo.clusterName] = []
        data.sort(sortFn)
        clusterPointData.value[queryInfo.clusterName] = data
        return {
          cell: 0
        }
      } else {
        let data = res.data.packages
        clusterPointData.value[queryInfo.clusterName] = {}
        let clusterIndex = queryInfo.clusterName.split('#')[0]
        data.forEach(item => {
          clusterPointData.value[queryInfo.clusterName][item.package_name] = {
            maxV: maxBy(item.points, (item) => {
              if (item.unit == 'mV') return item.value
            }),
            maxT: maxBy(item.points, (item) => {
              if (item.unit == '℃') return item.value
            }),
            minV: minBy(item.points, (item) => {
              if (item.unit == 'mV') return item.value
            }),
            minT: minBy(item.points, (item) => {
              if (item.unit == '℃') return item.value
            })
          }
          let packageIndex = item.package_name.split('#')[0]
          item.points.forEach(point => {
            let pointIndex = point.point_name.split('#')[0]
            // let cellVoltageIndex = packageIndex == 1 && clusterIndex == 1 ? pointIndex : pointIndex - (((packageIndex - 1) * 240) + (((clusterIndex - 1) * 5) * 240))
            // let cellTemperatureIndex = packageIndex == 1 && clusterIndex == 1 ? pointIndex : pointIndex - ((packageIndex - 1) * 120 + (((clusterIndex - 1) * 5) * 120))
            let cellVoltageIndex = pointIndex
            let cellTemperatureIndex = pointIndex
            if (point.unit == 'mV') {
              let voltage = clusterPointData.value[queryInfo.clusterName][item.package_name][cellVoltageIndex]?.voltage
              let temperature = clusterPointData.value[queryInfo.clusterName][item.package_name][cellVoltageIndex]?.temperature
              clusterPointData.value[queryInfo.clusterName][item.package_name][cellVoltageIndex] = {
                voltage: voltage ? voltage : point.unit == 'mV' ? point.value : '',
                temperature: temperature ? temperature : point.unit == '℃' ? point.value : ''
              }
            } else {
              let voltage = clusterPointData.value[queryInfo.clusterName][item.package_name][cellTemperatureIndex]?.voltage
              let temperature = clusterPointData.value[queryInfo.clusterName][item.package_name][cellTemperatureIndex]?.temperature
              clusterPointData.value[queryInfo.clusterName][item.package_name][cellTemperatureIndex] = {
                voltage: voltage ? voltage : point.unit == 'mV' ? point.value : '',
                temperature: temperature ? temperature : point.unit == '℃' ? point.value : ''
              }
            }
          })
        })
        return {
          cell: 1
        }
      }
    }

    /**
     * 告警录波
     */
    const exportAlarmRecordFileFn = async (data) => {
      const res = await exportAlarmRecordFile(JSON.stringify(data))
      if (!(res instanceof Blob) && res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      handleExport(res, data.fileName, '')
    }

    /**
     * 告警导出
     */
    const exportAlarmDataFn = async (data) => {
      const res = await exportAlarmData(data)
      if (!(res instanceof Blob) && res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      handleExport(res, `${data.start}_${data.end}_告警报表`)
    }

    return {
      treeData,
      deviceData,
      getDeviceDataFn,
      faultData,
      getFaultDataFn,
      getDeviceListFn,
      pageInfo,
      faultQueryInfo,
      greenList,
      redList,
      getStatusTypeFn,
      clusterData,
      clusterPointData,
      batteryClusterFn,
      batteryTypeDeviceId,
      batteryDataFn,
      currentAlarmCount,
      getCurrentAlarmFn,
      exportAlarmRecordFileFn,
      exportAlarmDataFn
    }
  },
  {
    persist: [
      {
        pick: ['treeData'],
        storage: localStorage
      }
    ]
    // persist: true
  }
)
