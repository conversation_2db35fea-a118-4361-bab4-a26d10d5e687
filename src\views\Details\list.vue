<script setup>
import { computed, ref, toRefs, watchEffect } from 'vue'
import { useDeviceStore } from '@/store/module/device'

const { treeData, deviceData } = toRefs(useDeviceStore())
const getStatusTypeFn = (status) => {
  return useDeviceStore().getStatusTypeFn(status)
}

const props = defineProps({
  deviceId: [String, Number]
})
const emits = defineEmits(['deviceItemClick'])
const data = ref([])
watchEffect(() => {
  if (props.deviceId == 0) {
    // data.value = [...treeData.value.filter((item) => item.device_hide == 0)]
    data.value = [...treeData.value]
  } else {
    data.value = deviceData.value[props.deviceId]
  }
})

const handleDeviceItemClick = (type, key, item) => {
  emits('deviceItemClick', type, key, item)
}
</script>

<template>
  <div class="pa-4">
    <div v-if="props.deviceId == 0" class="flex flex-wrap">
      <v-hover v-for="item in data" :key="item.deviceId">
        <template v-slot:default="{ isHovering, props }">
          <v-card
            variant="outlined"
            class="rounded-lg pa-4 ma-4 cursor-pointer"
            style="width: 300px; height: 280px; position: relative"
            v-bind="props"
            :elevation="isHovering ? 4 : 0"
            @click="handleDeviceItemClick(false, item.id)"
          >
            <div class="flex align-center mb-4">
              <img
                src="../../assets/img/device-pcs.png"
                alt=""
                style="width: 25px"
                v-if="
                  item.device_class == 'ACHMIDevice' ||
                  item.device_class == 'DCHMIDevice'
                "
              />
              <img
                src="../../assets/img/device-el.png"
                alt=""
                style="width: 25px"
                v-else-if="item.device_class == 'MeterDevice'"
              />
              <img
                src="../../assets/img/device-default.png"
                alt=""
                style="width: 25px"
                v-else
              />
              <div class="ml-2 font-600">{{ item.title }}</div>
            </div>
            <div class="flex align-center justify-center">
              <img
                src="../../assets//img/el.png"
                alt=""
                style="width: 200px; height: 190px"
                v-if="item.device_class == 'MeterDevice'"
              />
              <img
                src="../../assets//img/bms.png"
                alt=""
                style="width: 200px; height: 200px"
                class="pb-3"
                v-else-if="
                  item.device_class == 'BMSDevice' ||
                  item.device_class == 'BAUDevice'
                "
              />
              <img
                src="../../assets//img/pcs1.png"
                alt=""
                style="height: 190px"
                v-else-if="
                  item.device_class == 'ACHMIDevice' ||
                  item.device_class == 'DCHMIDevice'
                "
              />
              <img
                src="../../assets//img/pvinverter.png"
                alt=""
                style="width: 210px; height: 190px"
                v-else-if="item.device_class == 'pvinverter'"
              />
              <img
                src="../../assets//img/air.png"
                alt=""
                style="width: 230px; height: 200px"
                v-else-if="item.device_class == 'AirConditionerDevice'"
              />
              <img
                src="../../assets//img/fire.png"
                alt=""
                style="width: 210px; height: 190px"
                v-else-if="item.device_class == 'FireAlarmDevice'"
              />
              <img
                src="../../assets//img/Liquidcooling.png"
                alt=""
                style="width: 210px; height: 190px; margin-right: 20px"
                v-else-if="item.device_class == 'Liquidcooling'"
              />
              <img
                src="../../assets//img/Diesel.png"
                alt=""
                style="width: 210px; height: 190px; margin-right: 20px"
                v-else-if="item.device_class == 'Diesel'"
              />
              <img
                src="../../assets//img/ats.png"
                alt=""
                style="width: 160px; height: 190px; margin-right: 20px"
                v-else-if="item.device_class == 'ATS'"
              />
              <img
                src="../../assets//img/transformer.png"
                alt=""
                style="width: 190px; height: 190px; margin-right: 20px"
                v-else-if="item.device_class == 'Transformer'"
              />
              <img
                src="../../assets//img/default.png"
                alt=""
                style="width: 200px; height: 200px"
                v-else
              />
            </div>
            <div style="position: absolute; bottom: 20px; right: 20px">
              <img
                src="../../assets/img/on.png"
                alt=""
                style="width: 25px"
                v-if="item.device_status == 1"
              />
              <img
                src="../../assets/img/off.png"
                alt=""
                style="width: 25px"
                v-else-if="item.device_status == 0"
              />
              <img
                src="../../assets/img/both.png"
                alt=""
                style="width: 25px"
                v-else-if="item.device_status == 2"
              />
            </div>
          </v-card>
        </template>
      </v-hover>
    </div>
    <div v-else class="flex flex-wrap">
      <v-card
        variant="outlined"
        v-for="(value, key) in data"
        class="rounded-lg pa-4 ma-4"
        style="width: 300px; height: 250px"
        :key="key"
        @click="handleDeviceItemClick(true, key, value)"
      >
        <div class="flex align-center justify-space-between mb-2 w-100">
          <div class="flex align-center" style="width: 87%">
            <img
              src="../../assets/img/device-module.png"
              alt=""
              style="width: 25px"
            />
            <div class="ml-2 font-600 line1" style="width: 80%" :title="key">
              {{ key }}
            </div>
          </div>
          <img
            src="../../assets/img/more.png"
            alt=""
            style="width: 30px; height: 20px"
          />
        </div>
        <div>
          <template v-if="value?.status">
            <div v-for="item in 4" :key="item" class="py-3 flex align-center">
              <template v-if="value?.status[item - 1]">
                <img
                  src="../../assets/img/icon-red.png"
                  alt=""
                  style="width: 20px; height: 20px"
                  class="mr-2"
                  v-if="getStatusTypeFn(value.status[item - 1]?.value) == 2"
                />
                <img
                  src="../../assets/img/icon-enable.png"
                  alt=""
                  style="width: 20px; height: 20px"
                  class="mr-2"
                  v-else-if="
                    getStatusTypeFn(value.status[item - 1]?.value) == 1
                  "
                />
                <img
                  src="../../assets/img/circle.png"
                  alt=""
                  style="width: 20px; height: 20px"
                  class="mr-2"
                  v-else
                />
                <div
                  style="width: 90%"
                  class="line1"
                  :title="`${value.status[item - 1]?.point_name}：${
                    value.status[item - 1]?.value
                  }`"
                >
                  {{ value.status[item - 1]?.point_name }}：{{
                    value.status[item - 1]?.value
                  }}
                </div>
              </template>
            </div>
          </template>
          <template v-else>
            <div
              v-for="item in value.analog"
              :key="item.point_id"
              class="py-3 flex align-center"
            >
              <img
                src="../../assets/img/circle.png"
                alt=""
                style="width: 20px; height: 20px"
                class="mr-2"
              />
              <div
                style="width: 90%"
                class="line1"
                :title="`${item.point_name}：${item.value} ${item.unit}`"
              >
                {{ item.point_name }}：{{ item.value }} {{ item.unit }}
              </div>
            </div>
          </template>
        </div>
      </v-card>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.v-card--variant-outlined) {
  border: thin solid #ccc;
}
</style>
