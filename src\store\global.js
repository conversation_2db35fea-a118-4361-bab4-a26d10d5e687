/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-27 15:11:45
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-08-25 19:23:14
 * @FilePath: \ems_manage\src\store\global.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineStore } from 'pinia'
import { ref } from 'vue'
import {
  getLanguageList,
  getLanguageAll,
  getLanguage,
  setLanguageNo,
  setLanguage
} from '@/api/global'
import fs from 'node:fs'
import path from 'node:path'

export const useGlobalStore = defineStore(
  'global',
  () => {
    const snackbar = ref(false)
    const snackbarText = ref('')

    /**
     * 语言
     */
    const langArr = ref([])
    const getLanguageListFn = async () => {
      try {
        const res = await getLanguageList()
        langArr.value = res.languages
      } catch (error) {
        snackbar.value = true
        snackbarText.value = error
      }
    }
    const getLanguageAllFn = async () => {
      try {
        const res = await getLanguageAll()
      } catch (error) {
        snackbar.value = true
        snackbarText.value = error
      }
    }

    /**
     * 键盘
     */
    const showKeyboard = ref(false)
    const currentInput = ref(null)
    const confirmCall = ref(null)
    const keyboardDialog = ref(false)
    const keyboardInput = ref(null)
    const keyboardInputValue = ref('')
    const keyboardMode = ref('di_gt')
    const keyboardRange = ref([])
    const isShowKeyboard = ref(true)

    /**
     * 拖拽面板
     */
    const showPanel = ref(false)
    const panelType = ref('')

    /**
     * 收益
     */
    const legendSelected = ref({})

    return {
      snackbar,
      snackbarText,
      showKeyboard,
      currentInput,
      confirmCall,
      keyboardDialog,
      keyboardInput,
      keyboardInputValue,
      keyboardMode,
      keyboardRange,
      isShowKeyboard,
      showPanel,
      panelType,
      legendSelected,
      langArr,
      getLanguageListFn,
      getLanguageAllFn
    }
  },
  {
    persist: true
  }
)
